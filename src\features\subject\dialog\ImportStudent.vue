<template>
  <DialogForm ref="refDiaForm" :title="t('importStudents')" hide-actions width="1024px">
    {{ refDiaForm?.dialogRef?.modelValue }}
    <FieldBranchOptions
      v-model:branch-id="selectedBranchId"
      v-if="payload.length > 0"
    />
    <TableSheetJS
      :valid
      class="q-my-md"
      ref="sheet"
      @download-template="downloadTemplateForStudents"
      custom-actions
    />
    <q-btn
      v-if="haveItems"
      class="flex q-mx-auto fit"
      unelevated
      color="positive"
      :label="t('save')"
      @click="saveImported"
    ></q-btn>
  </DialogForm>
</template>

<script setup lang="ts">
import { useStudentStore } from 'src/stores/student';
import TableSheetJS from 'src/components/form/TableSheetJS.vue';
import DialogForm from 'src/components/form/DialogForm.vue';
import { downloadTemplateForStudents } from 'src/utils/file-template';
import { computed, ref, watch } from 'vue';
import { StudentService } from 'src/services/student';
import FieldBranchOptions from 'src/components/form/FieldBranchOptions.vue';
import { useI18n } from 'vue-i18n';
import type { SheetStudentPayload } from 'src/types/api';
import { useCurriculumStore } from 'src/stores/curriculum';

const refDiaForm = ref<InstanceType<typeof DialogForm>>();
const haveItems = computed(() => {
  return sheet.value?.items?.length > 0;
});
const selectedBranchId = ref<number | undefined>();
const store = useStudentStore();
const sheet = ref();
const validThreshold = ref<number>(0);
const valid = computed(() => validThreshold.value >= 2);
const { t } = useI18n();
const payload = ref<SheetStudentPayload[]>([]);

watch(
  () => selectedBranchId.value,
  (val) => {
    if (!val) return;
    if (val > 0) {
      payload.value.map((item) => (item.branchId = val || -1));
      validThreshold.value++;
    }
  },
);

watch(
  () => sheet.value?.items,
  (v) => {
    if (!v) return;
    const sc = JSON.parse(JSON.stringify(v));
    payload.value = sc.map(
      (s: { studentCode: string; thaiName: string; engName: string }) => ({
        code: String(s.studentCode),
        thaiName: s.thaiName,
        engName: s.engName,
        enrollmentDate: new Date(),
      }),
    );
    if (payload.value.length > 0) {
      validThreshold.value++;
    }
  },
);

async function saveImported() {
  // map curriculumId to payload
  payload.value.map(
    (item) => (item.curriculumId = useCurriculumStore().getInsertId),
  );
  await StudentService.postImportedStudents(payload.value);
  await store.fetchAll();
  if (refDiaForm.value?.dialogRef) {
    refDiaForm.value.dialogRef.hide();
  }
}
</script>

<style scoped></style>
