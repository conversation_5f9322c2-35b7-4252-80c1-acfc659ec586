<template>
  <div class="q-gutter-y-md q-mt-md">
    <PageTitle :title="t('ประวัติการให้คะแนน')" />

    <div class="row q-gutter-md">
      <q-select
        v-model="selectedYear"
        :options="yearOptions"
        label="เลือกปี"
        style="width: 150px; margin-bottom: 20px"
      />
      <q-select
        v-model="selectedSubject"
        :options="subjectOptions"
        label="วิชา"
        style="width: 150px; margin-bottom: 20px"
      />
    </div>

    <!-- Table -->
    <div style="overflow-x: auto; width: 100%">
      <q-table
        flat
        bordered
        class="q-animate--fade q-mt-md"
        separator="cell"
        :rows="mockData"
        row-key="code"
        :columns="skillCollectionColumns"
      >
        <template #body-cell-number="props">
          <q-td>{{ props.rowIndex + 1 }}</q-td>
        </template>
      </q-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
/*
    imports
*/
import { useI18n } from 'vue-i18n';

import PageTitle from 'src/components/common/PageTitle.vue';
import { onMounted, ref } from 'vue';
import type { QTableColumn } from 'quasar/dist/types/api.js';
import { useSkillStore } from 'src/stores/skill';
/*
    states
*/
const skillStore = useSkillStore();
const { t } = useI18n();
const selectedYear = ref('ชั้นปี 1');
const yearOptions = ['ชั้นปี 1', 'ชั้นปี 2', 'ชั้นปี 3', 'ชั้นปี 4'];
const selectedSubject = ref('วิชาคณิตศาสตร์');
const subjectOptions = [
  'วิชาคณิตศาสตร์',
  'วิชาวิทยาศาสตร์',
  'วิชาภาษาไทย',
  'วิชาภาษาอังกฤษ',
];

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function getSkillLevel(row: any, skillName: string): string {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const match = row.skills?.find((s: any) => s.name === skillName);
  return match ? match.level.toString() : '-';
}


const mockData = ref([
  {
    code: '64010001',
    thaiName: 'สมชาย ใจดี',
    skills: [
      { name: 'Software Development', level: 3 },
      { name: 'Database Systems', level: 2 },
      { name: 'Web Development', level: 2 },
      { name: 'Game Development', level: 1 }
    ]
  },
  {
    code: '64010002',
    thaiName: 'สุภาพร นิ่มนวล',
    skills: [
      { name: 'Data Science', level: 3 },
      { name: 'Artificial Intelligence', level: 2 },
      { name: 'Big Data', level: 2 },
      { name: 'Natural Language Processing', level: 1 }
    ]
  },
  {
    code: '64010003',
    thaiName: 'ก้องภพ เที่ยงธรรม',
    skills: [
      { name: 'Cloud Computing', level: 3 },
      { name: 'DevOps', level: 3 },
      { name: 'Cybersecurity', level: 2 },
      { name: 'Operating Systems', level: 2 }
    ]
  },
  {
    code: '64010004',
    thaiName: 'พรเทพ วัฒนชัย',
    skills: [
      { name: 'Embedded Systems', level: 3 },
      { name: 'Digital Signal Processing', level: 2 },
      { name: 'Computer Vision', level: 1 },
      { name: 'Software Testing', level: 2 }
    ]
  },
  {
    code: '64010005',
    thaiName: 'ชลธิชา สายสมร',
    skills: [
      { name: 'Mobile Development', level: 3 },
      { name: 'Agile Methodology', level: 2 },
      { name: 'Software Development', level: 1 },
      { name: 'Human-Computer Interaction', level: 2 }
    ]
  },
  {
    code: '64010006',
    thaiName: 'ณัฐพล บัวคำ',
    skills: [
      { name: 'Artificial Intelligence', level: 3 },
      { name: 'Computer Vision', level: 3 },
      { name: 'Natural Language Processing', level: 2 },
      { name: 'Big Data', level: 3 }
    ]
  }
])

// const curriculumSkills = ref([
//   { id: 1, name: 'Software Development' },
//   { id: 2, name: 'Database Systems' },
//   { id: 3, name: 'Artificial Intelligence' },
//   { id: 4, name: 'Data Science' },
//   { id: 5, name: 'Cybersecurity' },
//   { id: 6, name: 'Cloud Computing' },
//   { id: 7, name: 'Computer Networks' },
//   { id: 8, name: 'Operating Systems' },
//   { id: 9, name: 'Human-Computer Interaction' },
//   { id: 10, name: 'Mobile Development' },
//   { id: 11, name: 'DevOps' },
//   { id: 12, name: 'Software Testing' },
//   { id: 13, name: 'Agile Methodology' },
//   { id: 14, name: 'Web Development' },
//   { id: 15, name: 'Digital Signal Processing' },
//   { id: 16, name: 'Embedded Systems' },
//   { id: 17, name: 'Computer Vision' },
//   { id: 18, name: 'Natural Language Processing' },
//   { id: 19, name: 'Big Data' },
//   { id: 20, name: 'Game Development' }
// ])

const skillCollectionColumns = ref<QTableColumn[]>([
  {
    name: 'number',
    label: t('no.'),
    field: () => {},
    align: 'left',
  },
  {
    name: 'code',
    label: t('code'),
    field: (row) => row.code,
    align: 'left',
  },
  {
    name: 'thaiName',
    label: t('name'),
    field: 'thaiName',
    align: 'left',
  },

    ...skillStore.getSkills.flatMap((skill) => [
    {
      name: `skill_${skill.thaiName}`,
      label: skill.thaiName,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      field: (row: any): string => getSkillLevel(row, skill.thaiName),
      align: 'center' as const,
    },
  ]),
  {
    name: 'actions',
    label: t('actions'),
    field: '',
    align: 'left',
  },
]);

onMounted(async () => {
  await skillStore.fetchAllInCurr()
})
</script>
<style scoped>
.q-table__container {
  overflow-x: auto;
  max-width: 100%;
}

.q-table thead th,
.q-table tbody td {
  white-space: nowrap;
}
</style>
