import type { MenuProps } from 'src/components/ui/MenuLink.vue';
import { EnumUserRole } from './roles';

// Extended menu item interface with roles
interface MenuItemWithRoles extends MenuProps {
  roles: string[];
}

// Base menu items with their allowed roles
const menuItems: MenuItemWithRoles[] = [
  {
    title: 'dashboard',
    icon: 'home',
    link: '/dashboard',
    roles: [
      EnumUserRole.ADMIN,
      EnumUserRole.COORDINATOR,
      EnumUserRole.INSTRUCTOR,
      EnumUserRole.STUDENT,
    ],
  },
  {
    title: 'skills',
    icon: 'code',
    link: '/skills',
    roles: [
      EnumUserRole.ADMIN,
      EnumUserRole.COORDINATOR,
      EnumUserRole.INSTRUCTOR,
    ],
  },
  {
    title: 'subjects',
    icon: 'book',
    link: '/subjects',
    roles: [EnumUserRole.ADMIN],
  },
  {
    title: 'curriculums',
    icon: 'collections_bookmark',
    link: '/curriculums/management',
    roles: [EnumUserRole.ADMIN, EnumUserRole.COORDINATOR],
  },
  {
    title: 'faculties & branches',
    icon: 'groups',
    link: '/faculties',
    roles: [EnumUserRole.ADMIN],
  },
  // {
  //   title: 'courses',
  //   icon: 'play_lesson',
  //   link: '/courses',
  //   roles: [EnumUserRole.ADMIN, EnumUserRole.COORDINATOR, EnumUserRole.INSTRUCTOR, EnumUserRole.STUDENT],
  // },
  {
    title: 'users',
    icon: 'manage_accounts',
    link: '/users',
    roles: [EnumUserRole.ADMIN],
  },
  {
    title: 'instructors',
    icon: 'group',
    link: '/instructors/management',
    roles: [EnumUserRole.ADMIN],
  },
  {
    title: 'students',
    icon: 'school',
    link: '/students',
    roles: [EnumUserRole.ADMIN],
  },
];

// Function to get menu items based on user role
export const getMenuByRole = (userRole: string): MenuProps[] => {
  return menuItems
    .filter(item => item.roles.includes(userRole))
    .map(({ roles, ...menuProps }) => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const _ = roles; // Acknowledge that roles is intentionally unused in the destructuring
      return menuProps;
    });
};

// Individual role menus for convenience
export const adminMenu: MenuProps[] = getMenuByRole(EnumUserRole.ADMIN);
export const coordinatorMenu: MenuProps[] = getMenuByRole(EnumUserRole.COORDINATOR);
export const instructorMenu: MenuProps[] = getMenuByRole(EnumUserRole.INSTRUCTOR);
export const studentMenu: MenuProps[] = getMenuByRole(EnumUserRole.STUDENT);

// Keep the allMenu for backward compatibility (admin menu)
export const allMenu: MenuProps[] = adminMenu;
