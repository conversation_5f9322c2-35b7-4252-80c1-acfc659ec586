// Quasar SCSS (& Sass) Variables
// --------------------------------------------------
// To customize the look and feel of this app, you can override
// the Sass/SCSS variables found in Quasar's source Sass/SCSS files.

// Check documentation for full list of Quasar variables

// Your own variables (that are declared here) and Quasar's own
// ones will be available out of the box in your .vue/.scss/.sass files

// It's highly recommended to change the default colors
// to match your app's branding.
// Tip: Use the "Theme Builder" on Quasar's documentation website.

@import url('https://fonts.googleapis.com/css2?family=Sarabun:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

// COLORS
$primary: #3674b5;
$secondary: #578fca;
$accent: #face00;
$accent_2: #7c7c7c;
$surface_light: #fafafa;
$surface_secondary: #d1d7dd;
$primary_darker: #032345;
$surface-primary: #fefeff;

// DARK
$primary_dark: #323538;
$secondary_dark: #30373e;
$accent_dark: #face00;
$accent_2_dark: #7c7c7c;
$surface-dark: #202020;

$dark: #1d1d1d;
$dark-page: #121212;

$positive: #21ba45;
$negative: #c10015;
$info: #31ccec;
$warning: #f2c037;

// BUTTON
$button-border-radius: 10px;

// GENERIC
$generic-border-radius: 10px;
$body-font-size: var(--q-theme-font-size, 16px);
$typography-font-family: 'Sarabun', sans-serif !default;
$button-font-size: var(--q-theme-btn-font-size, 15px);
