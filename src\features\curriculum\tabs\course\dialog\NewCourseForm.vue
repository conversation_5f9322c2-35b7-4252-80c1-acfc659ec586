<template>
  <DialogForm
    width="70%"
    :title="t('form.New Course')"
    v-model="store.dialogState"
    @hide="
      () => {
        store.resetForm(), clearAllSelected();
      }
    "
    @save="addNewCourse()"
  >
    <div class="row">
      <div class="col-5 q-gutter-y-md">
        <div class="text-primary text-weight-bold">
          {{ t('curriculum') }}
        </div>

        <!-- ส่วน scroll -->
        <div style="max-height: 350px; overflow-y: auto">
          <q-list bordered separator class="rounded-borders">
            <q-item
              clickable
              v-for="item in curriculums"
              @click="setSelectedCurrCode(item.id!)"
              :key="item.id"
            >
              <q-item-section>
                {{ item.thaiName || item.engName }}
                <div class="text-grey-8 text-caption">
                  {{ t('code') }} {{ item.code }}
                </div>
              </q-item-section>
              <q-item-section side v-if="selectedCurrId === item.id">
                <q-icon name="done" color="positive" />
              </q-item-section>
            </q-item>
          </q-list>
        </div>
      </div>

      <div
        v-if="selectedCurrId"
        class="q-ml-md col q-gutter-y-md cursor-pointer"
      >
        <div class="text-primary text-weight-bold">
          {{ t('subject') }}
        </div>

        <q-scroll-area style="height: 350px; width">
          <q-list bordered separator class="rounded-borders">
            <q-item
              clickable
              v-for="item in subjects"
              @click="toggleSelectedSubject(item)"
              :key="item.id"
            >
              <q-item-section>
                {{ item.thaiName || item.engName }}
                <div class="text-grey-8 text-caption">
                  {{ t('subjectCode') }} {{ item.code }}
                </div>
              </q-item-section>
              <q-item-section side v-if="findExistSubjectCode(item.code)">
                <q-icon name="done" color="positive" />
              </q-item-section>
            </q-item>
          </q-list>
        </q-scroll-area>
      </div>

      <div v-else class="flex q-mx-auto items-center text-grey-6">
        {{ t('Please select a curriculum first') }}
      </div>
      <div v-if="selectedSubjects" class="col-12 q-mt-lg">
        <div class="q-gutter-x-sm q-mb-sm">
          <q-radio
            v-model="store.form.semester"
            val="1"
            :label="t('firstSemester')"
          ></q-radio>
          <q-radio
            v-model="store.form.semester"
            val="2"
            :label="t('secondSemester')"
          ></q-radio>
          <q-radio
            v-model="store.form.semester"
            val="3"
            :label="t('summerSemester')"
          ></q-radio>
        </div>
        <q-select
          v-model:model-value="academicYear"
          :options="options"
          :rules="[
            requireField,
            (val) => /^\d{4}$/.test(val) || 'Year must be a 4-digit number',
          ]"
          use-input
          hide-selected
          input-debounce="0"
          maxlength="4"
          fill-input
          @filter="filterFnYear"
          @input-value="setModelYear"
          :label="t('academicYear')"
          outlined
        />
      </div>
    </div>
  </DialogForm>
</template>

<script setup lang="ts">
import DialogForm from 'src/components/form/DialogForm.vue';
import { requireField } from 'src/utils/field-rules';
import { CourseService } from 'src/services/course';
import { useCourseStore } from 'src/stores/course';
import { onMounted, ref, watch } from 'vue';
import { SubjectService } from 'src/services/subject';
import { useI18n } from 'vue-i18n';
import type { Curriculum, Subject } from 'src/types/education';
import { CurriculumService } from 'src/services/curriculums';
import { getYears } from 'src/utils/years';

const store = useCourseStore();
const selectedSubjects = ref<Subject[]>([]);
const subjects = ref<Subject[]>([]);
const academicYear = ref<string | undefined>(undefined);

const selectedCurrId = ref<number>();
const { t } = useI18n();
const curriculums = ref<Curriculum[]>([]);

onMounted(async () => {
  const { data } = await CurriculumService.getAll();
  curriculums.value = data;
});

const stringOptions: string[] = getYears().map(String);
const options = ref<string[]>([...stringOptions]);

function filterFnYear(val: string, update: (update: () => void) => void) {
  if (val === '') {
    update(() => {
      options.value = [...stringOptions];
    });
    return;
  }
  update(() => {
    const needle = val.toLowerCase();
    options.value = stringOptions.filter(
      (v) => v.toLowerCase().indexOf(needle) > -1,
    );
  });
}

function setModelYear(val:string) {
  store.form.year = Number(val);
}

const fetchSubjectInCourse = async (id: number) => {
  const { data } = await SubjectService.getAll({ curriculumId: id });

  subjects.value = data;
  console.log(subjects.value);
};

watch(selectedCurrId, async (val) => {
  if (val) {
    await fetchSubjectInCourse(val);
  }
});

const clearSelectedSubjects = () => {
  selectedSubjects.value = [];
};
const clearSelectedCurriculum = () => {
  selectedCurrId.value = undefined;
};
const setSelectedCurrCode = (id: number) => {
  selectedCurrId.value = id;
  clearSelectedSubjects();
};
const addSelectedSubject = (subject: Subject) => {
  selectedSubjects.value.push(subject);
};
const removeSelectedSubject = (subject: Subject) => {
  selectedSubjects.value = selectedSubjects.value?.filter(
    (s) => s.code !== subject.code,
  );
};

const findExistSubjectCode = (code: string) => {
  return selectedSubjects.value.some((s) => s.code === code);
};
const toggleSelectedSubject = (subject: Subject) => {
  if (findExistSubjectCode(subject.code)) {
    removeSelectedSubject(subject);
  } else {
    addSelectedSubject(subject);
  }
};

const clearAllSelected = () => {
  clearSelectedSubjects();
  clearSelectedCurriculum();
};

const addNewCourse = async () => {
  try {
    if (
      !store.form.semester ||
      !store.form.year ||
      selectedSubjects.value.length === 0
    ) {
      return;
    }

    const payload = selectedSubjects.value.map((subject) => ({
      active: store.form.active || true,
      semester: parseInt(store.form.semester?.toString()), // 1, 2, or 3
      year: parseInt(store.form.year?.toString()), // 2568
      subjectId: subject.id,
    }));

    await CourseService.createMany(payload);

    store.dialogState = false;
    clearAllSelected();
    store.resetForm();
    await store.fetchAll();
  } catch (err) {
    console.error('❌ Failed to create course:', err);
  }
};
</script>
