<template>
    <div class="q-pa-md">
        <div class="q-mb-md">
            <q-btn flat icon="arrow_back" label="Back to Summary" @click="goBack" class="q-mb-md" />
        </div>

        <div class="q-mb-md">
            <h5 class="q-my-none">Student List</h5>
            <p class="text-grey-6 q-mb-none">
                Skill ID: {{ skillId }}
                <span v-if="category" class="q-ml-md">
                    Category: {{ category }}
                </span>
                <span v-if="year" class="q-ml-md">
                    Year: {{ year }}
                </span>
            </p>
        </div>

        <!-- Placeholder for student list content -->
        <div class="q-pa-lg text-center text-grey-6">
            <q-icon name="group" size="4rem" class="q-mb-md" />
            <div class="text-h6">Student List Component</div>
            <div class="q-mt-sm">
                This will show students for skill ID: {{ skillId }}
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();

// Get parameters from route
const skillId = computed(() => route.params.skillId as string);
const category = computed(() => route.query.category as string);
const year = computed(() => route.query.year as string);

// Navigation function to go back
const goBack = () => {
    void router.push({
        name: 'Summary of Curriculum',
        params: { code: route.params.code },
        query: {
            skillType: route.query.skillType,
            year: route.query.year
        }
    });
};

// Log the received parameters for debugging
console.log('SummaryStudentList - Skill ID:', skillId.value);
console.log('SummaryStudentList - Category:', category.value);
console.log('SummaryStudentList - Year:', year.value);
</script>