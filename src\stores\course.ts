import { defineStore } from 'pinia';
import { Dialog, Notify, type QTable } from 'quasar';
import { CourseService } from 'src/services/course';
import { SkillCollectionService } from 'src/services/skill-collections';
import type { CLO, Course, StudentScore } from 'src/types/education';
import { convertToQuery, defaultPagination } from 'src/utils/pagination';
import useFilterStore from './filter-search';

export interface GroupCourse {
  group: string;
  courses: Course[];
}

export const useCourseStore = defineStore('course', {
  state: () => ({
    form: {} as Course,
    courses: [] as Course[],
    pagination: { ...defaultPagination },
    listStudentScore: [] as StudentScore[],
    dialogState: false,
    selectedClo: {} as CLO,
  }),

  getters: {
    getCourseId(state): number {
      const id = this.router.currentRoute.value.params.id as unknown as number;
      return id || state.form?.id || -1;
    },
    getCourse(): Partial<Course> {
      return this.form || {};
    },
    getRowClo: (state) => state.form.subject?.clos || [],
    getStudentsScores: (state) => state.listStudentScore,
    getSelectedCloId: (state) => state.selectedClo.id,
    getGroupCourse: (state) => {
      const grouped = state.courses.reduce((acc, course) => {
        // Create a unique key for the section using year and semester

        const sectionKey = `${course.year}-${course.semester}`;

        // Find the group in the accumulator
        let group = acc.find((g) => g.group === sectionKey);

        if (!group) {
          // If the group doesn't exist, create a new one
          group = {
            group: sectionKey, // The unique key for the group
            courses: [], // Initialize the courses array
          };
          acc.push(group); // Add the new group to the accumulator
        }

        // Add the course to the group's courses array
        group.courses.push(course);

        return acc;
      }, [] as GroupCourse[]); // Initialize as an array of GroupCourse

      return grouped;
    },
  },

  actions: {
    async fetchAll(pag?: QTable['pagination'], currId?: number) {
      const filterStore = useFilterStore();
      filterStore.filters.courses.curriculumId = currId;
      const response = await CourseService.getAll(
        convertToQuery(pag || this.pagination, filterStore.filters.courses),
      );
      this.courses = response.data;
    },
    async fetchStudentScores(
      courseId: string | number,
      cloId: string | number,
    ) {
      const res = await SkillCollectionService.getAll(
        String(courseId),
        String(cloId),
      );
      this.listStudentScore = res;
    },
    removeOne(id: number) {
      Dialog.create({
        title: 'Delete Course',
        message: 'Are you sure you want to delete this course?',
        cancel: true,
        persistent: true,
      }).onOk(() => {
        CourseService.removeOne(id)
          .then(async () => {
            Notify.create({
              type: 'positive',
              message: 'Deleted successfully',
            });
            await this.fetchAll();
          })
          .catch((err) => {
            Notify.create({
              type: 'negative',
              message: err.message,
            });
          });
      });
    },

    async fetchCourseId(id: string) {
      const response = await CourseService.getOne(id);
      this.form = response;
    },
    resetForm() {
      this.form = {} as Course;
    },
    async clickViewCourse(curriculumCode: string, courseId: number) {
      if (curriculumCode && courseId) {
        await this.router.push({
          name: 'course-details',
          params: { code: curriculumCode, id: courseId },
        });
      }
    },
  },
});
