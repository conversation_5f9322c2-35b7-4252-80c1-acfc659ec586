<template>
  <q-page>
    <q-tabs v-model="tab" dense align="justify" no-caps narrow-indicator>
      <q-tab name="hard" label="Hard Skills" />
      <q-tab name="soft" label="Soft Skills" />
    </q-tabs>
    <q-separator />
    <q-tab-panels v-model="tab" animated>
      <q-tab-panel name="hard" v-show="tab === 'hard'">
        <SummarySkillChart :skillType="tab" :key="'hard'"></SummarySkillChart>
      </q-tab-panel>
      <q-tab-panel name="soft" v-show="tab === 'soft'">
        <SummarySkillChart :skillType="tab" :key="'soft'"></SummarySkillChart>
      </q-tab-panel>
    </q-tab-panels>
  </q-page>
</template>

<script setup lang="ts">
import { useCurriculumStore } from 'src/stores/curriculum';
import { computed, onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import SummarySkillChart from '../summary/SummarySkillChart.vue';
import useFilterStore from 'src/stores/filter-search';
const route = useRoute();
const router = useRouter();
const filterStore = useFilterStore()
const currentYear = filterStore.filters.courses.years
const lastTwoDigits = currentYear && currentYear.length > 0
  ? (currentYear[0] % 100).toString().padStart(2, '0')
  : '';
const skillParam = route.query.skillType;
const yearParam = ref(route.query.year as string || currentYear);

const tab = ref(skillParam === 'soft' ? 'soft' : 'hard');

// Watch for route parameter changes
watch(
  () => route.query.skillType,
  (newSkillType) => {
    if (newSkillType && (newSkillType === 'soft' || newSkillType === 'hard')) {
      tab.value = newSkillType;
    }
  }
);

// Watch for year parameter changes
watch(
  () => route.query.year,
  (newYear) => {
    if (newYear && typeof newYear === 'string') {
      yearParam.value = newYear;
    }
  }
);

// Watch for tab changes and update URL
watch(
  () => tab.value,
  async (val) => {
    await router.replace({
      path: route.path,
      query: {
        skillType: val,
        year: yearParam.value
      }
    });
  },
  { immediate: true }
);
const store = useCurriculumStore();
const currCode = computed(() => route.params.code as string);

onMounted(async () => {
  if (!store.getInsertId) {
    await store.fetchOneByCode(currCode.value);
  }

  // Ensure default year is set in URL if not present
  if (!route.query.year) {
    await router.replace({
      path: route.path,
      query: {
        ...route.query,
        year: lastTwoDigits
      }
    });
  }
});
</script>

<style scoped>
.sum-card {
  width: 100%;
  min-width: 400px;
  min-height: 300px;
  max-height: 300px;
  overflow: hidden;
}

.panel-active {
  background-color: #ffffff;
  /* หรือสีอ่อนๆเช่น #f9f9f9 */
  border: 1px solid #ddd;
  border-top: none;
  border-radius: 0 0 8px 8px;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}
</style>
