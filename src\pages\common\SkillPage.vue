<script setup lang="ts">
import { computed, onMounted } from 'vue';
import { useSkillStore } from 'src/stores/skill';
import { useMeta } from 'quasar';
import { useRoute } from 'vue-router';
import PageHeader from 'src/components/common/PageHeader.vue';
import { useI18n } from 'vue-i18n';
import ChipSkill from 'src/components/common/ChipSkill.vue';
import useFilterStore from 'src/stores/filter-search';
import { watch } from 'vue';

const filterStore = useFilterStore();
const store = useSkillStore();
const { t } = useI18n();
const route = useRoute();
const title = computed(() => route.matched[1]?.name as string);

const paginationPage = computed({
  get: () => store.pagination.page || 1,
  set: (value) => {
    store.pagination.page = value;
  },
});

onMounted(async () => {
  await store.fetchAll();
});

watch(
  () => paginationPage.value,
  async (newValue) => {
    if (newValue) {
      await store.fetchAll({ ...store.pagination, page: newValue });
    }
  },
);

useMeta({
  title: t(`menuLinks.${title.value}`),
});
</script>

<template>
  <q-page padding>
    <PageHeader
      v-model:search-text="filterStore.search"
      :label-search="t('search') + t('skills')"
      @open-dialog="store.toggleDialog({ title: 'form.New Skill', index: -1 })"
      @enter-search="store.fetchAll"
      hide-add-btn
    />
    <q-toggle
      class="q-mt-md"
      v-model="store.onlyHaveSubs"
      :label="t('showOnlyWithSubSkill')"
    ></q-toggle>
    <!-- Content -->
    <q-scroll-area
      style="width: 100%; height: calc(100vh - 300px)"
      class="q-mt-md"
    >
      <q-card flat class="q-animate--fade bg-grey-1">
        <q-tree
          :nodes="store.getSkills"
          node-key="id"
          children-key="subs"
          class="q-pa-sm"
        >
          <template v-slot:default-header="props">
            <q-tr class="full-width q-py-xs hover-row" style="cursor: pointer">
              <!-- Header -->
              <q-td style="user-select: none">
                <div class="text-body1">
                  {{ props.node.thaiName }}
                  <ChipSkill :domain="props.node.domain" />
                </div>
                <div class="text-caption text-grey-7">
                  {{ props.node.engName }}
                </div>
              </q-td>
            </q-tr>
          </template>
        </q-tree>
      </q-card>
    </q-scroll-area>
    <div class="flex q-my-lg" v-show="store.getMaxPage > 1">
      <q-pagination
        v-if="store.getMaxPage > 0"
        class="q-mx-auto"
        active-design="unelevated"
        v-model="paginationPage"
        @update:model-value="(page) => (store.pagination.page = page)"
        :max="store.getMaxPage"
        direction-links
      />
    </div>
  </q-page>
</template>

<style scoped lang="scss">
.hover-row:hover {
  color: $secondary;
}
.hover-row1:hover {
  color: $secondary;
}
</style>
