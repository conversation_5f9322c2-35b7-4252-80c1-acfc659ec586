<template>
  <q-drawer
    v-model="drawerState"
    :width="270"
    show-if-above
    side="left"
    :mini="miniState"
    bordered
  >
    <slot></slot>
    <q-separator></q-separator>
    <MenuLink title="logout" link="/" @click="auth.logout" icon="logout" />
  </q-drawer>
</template>

<script setup lang="ts">
import { useGlobalStore } from 'src/stores/global';
import MenuLink from './MenuLink.vue';
import { useAuthStore } from 'src/stores/auth';
import { ref, watch } from 'vue'; // Added watch

const drawerState = ref(true);
const miniState = ref(false);

const app = useGlobalStore();
const auth = useAuthStore();

watch(
  () => app.leftDrawerOpen,
  (val) => {
    miniState.value = !val; // Update miniState based on leftDrawerOpen
  },
);

// const handleMouseEnter = () => {
//   if (app.leftDrawerOpen) return;
//   miniState.value = false;
// };

// const handleMouseLeave = () => {
//   if (app.leftDrawerOpen) return;
//   miniState.value = true;
// };
</script>
