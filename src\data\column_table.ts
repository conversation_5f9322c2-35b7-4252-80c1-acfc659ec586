import type { QTableColumn } from 'quasar';
import { i18n } from 'src/boot/i18n';
const { t } = i18n.global;

export const studentColumns: QTableColumn[] = [
  {
    name: 'code',
    label: t('code'),
    field: 'code',
    align: 'left',
    sortable: true,
  },
  {
    name: 'email',
    label: t('email'),
    field: (s) => s.code + '@go.buu.ac.th',
    align: 'left',
    sortable: true,
  },
  {
    name: 'name',
    label: t('name'),
    field: 'thaiName',
    align: 'left',
  },
  {
    name: 'engName',
    label: t('engName'),
    field: 'engName',
    align: 'left',
  },
  {
    name: 'branch',
    label: t('branch'),
    field: (s) => s.branch?.thaiName,
    align: 'left',
  },
  {
    name: 'actions',
    label: t('actions'),
    field: '',
    align: 'left',
  },
];

export const subjectTabColumns = <QTableColumn[]>[
  {
    name: 'number',
    label: t('no.'),
    field: () => {},
    align: 'left',
    sortable: true,
  },
  {
    name: 'code',
    label: t('code'),
    field: (row) => row.code,
    align: 'left',
    sortable: true,
  },
  {
    name: 'thaiName',
    label: t('name'),
    field: 'thaiName',
    align: 'left',
  },
  {
    name: 'engName',
    label: t('engName'),
    field: 'engName',
    align: 'left',
  },
  {
    name: 'credit',
    label: t('credit'),
    field: (row) => row.credit,
    align: 'left',
  },
  {
    name: 'type',
    label: t('type'),
    field: (row) => row.type,
    align: 'left',
  },
  {
    name: 'actions',
    label: t('actions'),
    field: '',
    align: 'left',
  },
];

export const subjectColumns = <QTableColumn[]>[
  { name: 'code', label: t('code'), field: 'code', align: 'left' },
  { name: 'name', label: t('name'), field: 'thaiName', align: 'left' },
  { name: 'engName', label: t('engName'), field: 'engName', align: 'left' },
  {
    name: 'description',
    label: t('thaiDescription'),
    field: 'thaiDescription',
    align: 'left',
  },
  { name: 'type', label: t('type'), field: 'type', align: 'left' },
  { name: 'credit', label: t('credit'), field: 'credit', align: 'left' },
  {
    name: 'curriculums',
    label: t('curriculum'),
    field: 'curriculums',
    align: 'left',
  },
];

export const ploColumns = <QTableColumn[]>[
  {
    name: 'number',
    label: t('no.'),
    field: 'no',
    align: 'left',
    style: 'width: 60px',
  },
  {
    name: 'name',
    label: t('name'),
    field: 'name',
    align: 'left',
    style: 'width: 150px',
  },
  {
    name: 'thaiDescription',
    label: t('thaiDescription'),
    field: 'thaiDescription',
    align: 'left',
    style:
      'max-width: 300px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;',
  },
  {
    name: 'engDescription',
    label: t('engDescription'),
    field: 'engDescription',
    align: 'left',
    style:
      'max-width: 300px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;',
  },
  {
    name: 'type',
    label: t('type'),
    field: 'type',
    align: 'left',
  },
  {
    name: 'actions',
    label: t('actions'),
    field: '',
    align: 'left',
  }, // กำหนดขนาด
];

export const coordinatorColumns = <QTableColumn[]>[
  {
    name: 'number',
    label: t('no.'),
    field: () => {},
    align: 'left',
    sortable: true,
  },
  {
    name: 'code',
    label: t('code'),
    field: (c) => c.code || 'Unknown',
    align: 'left',
    sortable: true,
  },
  {
    name: 'position',
    label: t('position'),
    field: (c) => c.position || 'Unknown',
    align: 'left',
  },
  {
    name: 'name',
    label: t('name'),
    field: 'thaiName',
    align: 'left',
  },
  {
    name: 'engName',
    label: t('engName'),
    field: 'engName',
    align: 'left',
  },
  {
    name: 'email',
    label: t('email'),
    field: 'email',
    align: 'left',
    sortable: true,
  },
  {
    name: 'actions',
    label: t('actions'),
    field: () => {},
    align: 'left',
  },
];

export const userColumns = <QTableColumn[]>[
  {
    name: 'id',
    label: 'ID',
    field: 'id',
    align: 'left',
    sortable: true,
  },
  {
    name: 'email',
    label: t('email'),
    field: 'email',
    align: 'left',
  },
  {
    name: 'role',
    label: t('role'),
    field: 'role',
    align: 'left',
  },
];