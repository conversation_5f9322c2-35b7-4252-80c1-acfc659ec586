<template>
  <q-header reveal :reveal-offset="0" bordered>
    <q-toolbar>
      <q-btn
        v-if="!hideToggle"
        flat
        dense
        icon="menu"
        aria-label="Menu"
        @click="app.toggleLeftDrawer()"
      />

      <q-toolbar-title>
        <router-link
          to="/"
          class="cursor-pointer text-grey-10"
          style="text-decoration: none"
        >
          <q-img
            src="logos/buu-white.svg"
            alt="logo"
            height="auto"
            width="50px"
            fit="contain"
          >
          </q-img>
          <span class="q-ml-sm text-weight-bold text-white">Skill Mapping</span>
        </router-link>
      </q-toolbar-title>

      <!-- Profile -->
      <div class="row items-center">
        <div
          class="bg-white q-px-sm q-py-xs rounded-borders text-black q-mr-md text-bold cursor-pointer non-selectable"
        >
          <div class="text-caption">{{ auth.getName }}</div>
        </div>

        <q-menu :offset="[-20, 0]" style="width: auto" class="shadow-1">
          <q-list>
            <q-item>
              <q-item-section side>
                <q-icon
                  color="primary"
                  :name="symOutlinedShieldPerson"
                ></q-icon>
              </q-item-section>
              <q-item-section class="text-primary text-bold">
                {{ auth.getRole ?? 'Unknown' }}
              </q-item-section>
            </q-item>
            <q-separator />

            <q-item
              v-close-popup
              clickable
              @click="app.toggleRightDrawer('settings')"
            >
              <q-item-section side>
                <q-icon name="settings"></q-icon>
              </q-item-section>
              <q-item-section> {{ t('settings') }} </q-item-section>
            </q-item>
            <q-item
              v-close-popup
              clickable
              @click="async () => await auth.logout()"
              to="/login"
            >
              <q-item-section side>
                <q-icon name="logout"></q-icon>
              </q-item-section>
              <q-item-section>
                {{ t('logout') }}
              </q-item-section>
            </q-item>
          </q-list>
        </q-menu>
      </div>

      <q-btn
        v-if="!auth.getAccessToken"
        label="login"
        to="/login"
        flat
        class="bg-primary text-bold"
        color="white"
      />
    </q-toolbar>
    <slot name="additional"> </slot>
  </q-header>
</template>

<script lang="ts" setup>
/*
    imports
*/
import { symOutlinedShieldPerson } from '@quasar/extras/material-symbols-outlined';
import { useAuthStore } from 'src/stores/auth';
import { useGlobalStore } from 'src/stores/global';
import { useI18n } from 'vue-i18n';
/*
    states
*/

defineProps<{
  hideToggle?: true;
}>();

const { t } = useI18n();
const auth = useAuthStore();

const app = useGlobalStore();
</script>
